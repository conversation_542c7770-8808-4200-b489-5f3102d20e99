<?php
require_once 'card_checker.php';

$checker = new CardChecker();
$result = null;
$error = null;

if ($_POST['action'] ?? '' === 'check_card') {
    $cardNumber = $_POST['card_number'] ?? '';
    
    if (empty($cardNumber)) {
        $error = "Por favor, insira um número de cartão.";
    } else {
        try {
            $result = $checker->analyzeCard($cardNumber);
        } catch (Exception $e) {
            $error = "Erro: " . $e->getMessage();
        }
    }
}

if ($_POST['action'] ?? '' === 'add_card') {
    $cardNumber = $_POST['new_card'] ?? '';
    
    if (empty($cardNumber)) {
        $error = "Por favor, insira um número de cartão para adicionar.";
    } else {
        try {
            if ($checker->addCard($cardNumber)) {
                $checker->saveCards();
                $success = "Cartão adicionado com sucesso!";
            } else {
                $error = "Cartão já existe na lista.";
            }
        } catch (Exception $e) {
            $error = "Erro: " . $e->getMessage();
        }
    }
}

if ($_POST['action'] ?? '' === 'remove_card') {
    $cardNumber = $_POST['remove_card'] ?? '';
    
    if (empty($cardNumber)) {
        $error = "Por favor, insira um número de cartão para remover.";
    } else {
        try {
            if ($checker->removeCard($cardNumber)) {
                $checker->saveCards();
                $success = "Cartão removido com sucesso!";
            } else {
                $error = "Cartão não encontrado na lista.";
            }
        } catch (Exception $e) {
            $error = "Erro: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificador de Cartões e BINs</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .dark {
            background-color: #1a202c;
            color: #e2e8f0;
        }
        .dark .bg-white {
            background-color: #2d3748;
        }
        .dark .text-gray-900 {
            color: #e2e8f0;
        }
        .dark .border-gray-300 {
            border-color: #4a5568;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen" id="body">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Verificador de Cartões e BINs</h1>
            <button onclick="toggleDarkMode()" class="bg-gray-800 text-white px-4 py-2 rounded hover:bg-gray-700">
                🌙 Modo Escuro
            </button>
        </div>

        <!-- Mensagens -->
        <?php if ($error): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Verificação de Cartão -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Verificar Cartão</h2>
                
                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="check_card">
                    
                    <div>
                        <label for="card_number" class="block text-sm font-medium text-gray-700 mb-2">
                            Número do Cartão
                        </label>
                        <input 
                            type="text" 
                            id="card_number" 
                            name="card_number" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="1234 5678 9012 3456"
                            value="<?php echo htmlspecialchars($_POST['card_number'] ?? ''); ?>"
                        >
                    </div>
                    
                    <button 
                        type="submit" 
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        Verificar Cartão
                    </button>
                </form>

                <!-- Resultado da Verificação -->
                <?php if ($result): ?>
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-semibold mb-3">Resultado da Análise</h3>
                        
                        <div class="space-y-2">
                            <p><strong>Número:</strong> <?php echo htmlspecialchars($result['formatted_number']); ?></p>
                            <p><strong>Válido:</strong> 
                                <span class="<?php echo $result['is_valid'] ? 'text-green-600' : 'text-red-600'; ?>">
                                    <?php echo $result['is_valid'] ? 'Sim' : 'Não'; ?>
                                </span>
                            </p>
                            <p><strong>Bandeira:</strong> <?php echo htmlspecialchars($result['brand']); ?></p>
                            <p><strong>Na Lista:</strong> 
                                <span class="<?php echo $result['in_list'] ? 'text-green-600' : 'text-red-600'; ?>">
                                    <?php echo $result['in_list'] ? 'Sim' : 'Não'; ?>
                                </span>
                            </p>
                            
                            <?php if ($result['bin_info']): ?>
                                <div class="mt-4">
                                    <h4 class="font-semibold">Informações da BIN:</h4>
                                    <div class="ml-4 space-y-1">
                                        <p><strong>BIN:</strong> <?php echo htmlspecialchars($result['bin_info']['bin']); ?></p>
                                        <p><strong>Emissor:</strong> <?php echo htmlspecialchars($result['bin_info']['issuer']); ?></p>
                                        <p><strong>Tipo:</strong> <?php echo htmlspecialchars($result['bin_info']['type']); ?></p>
                                        <p><strong>País:</strong> <?php echo htmlspecialchars($result['bin_info']['country']); ?></p>
                                        <?php if (!empty($result['bin_info']['website'])): ?>
                                            <p><strong>Website:</strong> 
                                                <a href="<?php echo htmlspecialchars($result['bin_info']['website']); ?>" 
                                                   target="_blank" class="text-blue-600 hover:underline">
                                                    <?php echo htmlspecialchars($result['bin_info']['website']); ?>
                                                </a>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <p class="text-gray-600 mt-4">Informações da BIN não encontradas.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Gerenciamento de Lista -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Gerenciar Lista de Cartões</h2>
                
                <!-- Adicionar Cartão -->
                <form method="POST" class="space-y-4 mb-6">
                    <input type="hidden" name="action" value="add_card">
                    
                    <div>
                        <label for="new_card" class="block text-sm font-medium text-gray-700 mb-2">
                            Adicionar Cartão
                        </label>
                        <input 
                            type="text" 
                            id="new_card" 
                            name="new_card" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                            placeholder="1234567890123456"
                        >
                    </div>
                    
                    <button 
                        type="submit" 
                        class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                        Adicionar à Lista
                    </button>
                </form>

                <!-- Remover Cartão -->
                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="remove_card">
                    
                    <div>
                        <label for="remove_card" class="block text-sm font-medium text-gray-700 mb-2">
                            Remover Cartão
                        </label>
                        <input 
                            type="text" 
                            id="remove_card" 
                            name="remove_card" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                            placeholder="1234567890123456"
                        >
                    </div>
                    
                    <button 
                        type="submit" 
                        class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                    >
                        Remover da Lista
                    </button>
                </form>
            </div>
        </div>

        <!-- Lista de Cartões -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Cartões na Lista (<?php echo count($checker->getAllCards()); ?>)</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php foreach ($checker->getAllCards() as $card): ?>
                    <div class="bg-gray-50 p-3 rounded border">
                        <code class="text-sm"><?php echo htmlspecialchars(chunk_split($card, 4, ' ')); ?></code>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <script>
        function toggleDarkMode() {
            const body = document.getElementById('body');
            const isDark = body.classList.contains('dark');
            
            if (isDark) {
                body.classList.remove('dark');
                body.classList.add('bg-gray-100');
                body.classList.remove('bg-gray-900');
                localStorage.setItem('darkMode', 'false');
            } else {
                body.classList.add('dark');
                body.classList.remove('bg-gray-100');
                body.classList.add('bg-gray-900');
                localStorage.setItem('darkMode', 'true');
            }
        }

        // Carregar preferência de modo escuro
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode');
            if (darkMode === 'true') {
                toggleDarkMode();
            }
        });

        // Formatação automática do número do cartão
        document.getElementById('card_number').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
            e.target.value = value;
        });
    </script>
</body>
</html>
