<?php
require_once 'card_checker.php';

$checker = new CardChecker();
$error = null;
$success = null;
$selectedBin = $_GET['bin'] ?? null;
$searchTerm = $_GET['search'] ?? '';
$binSearch = $_GET['bin_search'] ?? '';

try {
    $stats = $checker->getDatabaseStats();
    $binsByCountry = $checker->getUniqueBinsFromDatabase();

    // Filtrar BINs por busca de bandeira/emissor
    if (!empty($binSearch)) {
        $binsByCountry = $checker->filterBinsBySearch($binSearch, $binsByCountry);
    }

    $selectedCards = [];
    $binInfo = null;

    if ($selectedBin) {
        $selectedCards = $checker->getCardsByBin($selectedBin);
        $binInfo = $checker->getBinInfo($selectedBin . '000000'); // Adiciona zeros para buscar info
    }

    $searchResults = [];
    if (!empty($searchTerm)) {
        $searchResults = $checker->searchCards($searchTerm);
    }

} catch (Exception $e) {
    $error = "Erro: " . $e->getMessage();
    $stats = ['total_cards' => 0, 'unique_bins' => 0, 'origins' => []];
    $binsByCountry = [];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificador de Cartões e BINs</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .dark {
            background-color: #1a202c;
            color: #e2e8f0;
        }
        .dark .bg-white {
            background-color: #2d3748;
        }
        .dark .text-gray-900 {
            color: #e2e8f0;
        }
        .dark .border-gray-300 {
            border-color: #4a5568;
        }

        .country-btn {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .country-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .country-bins {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .compact-mode {
            max-height: 200px;
            overflow: hidden;
            position: relative;
        }

        .compact-mode::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(transparent, white);
            pointer-events: none;
        }

        .copy-btn {
            transition: all 0.2s ease;
            font-size: 12px;
        }

        .copy-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #copy-notification {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen" id="body">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Verificador de Cartões por BIN</h1>
                <p class="text-gray-600 mt-2">Selecione uma BIN para visualizar os cartões associados</p>
            </div>
            <button onclick="toggleDarkMode()" class="bg-gray-800 text-white px-4 py-2 rounded hover:bg-gray-700">
                🌙 Modo Escuro
            </button>
        </div>

        <!-- Estatísticas -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-2xl font-bold text-blue-600"><?php echo number_format($stats['total_cards']); ?></h3>
                <p class="text-gray-600">Total de Cartões</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-2xl font-bold text-green-600"><?php echo number_format($stats['unique_bins']); ?></h3>
                <p class="text-gray-600">BINs Únicas</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-2xl font-bold text-purple-600"><?php echo count($binsByCountry); ?></h3>
                <p class="text-gray-600">Países</p>
            </div>
        </div>

        <!-- Mensagens -->
        <?php if ($error): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <!-- Busca -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">🔍 Buscar</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <!-- Busca de Cartões -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Buscar Cartões</label>
                    <form method="GET" class="flex gap-2">
                        <?php if (!empty($binSearch)): ?>
                            <input type="hidden" name="bin_search" value="<?php echo htmlspecialchars($binSearch); ?>">
                        <?php endif; ?>
                        <input
                            type="text"
                            name="search"
                            value="<?php echo htmlspecialchars($searchTerm); ?>"
                            placeholder="Buscar por número, nome ou CPF..."
                            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                        <button
                            type="submit"
                            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            Buscar
                        </button>
                    </form>
                </div>

                <!-- Busca de BINs -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Filtrar BINs</label>
                    <form method="GET" class="flex gap-2">
                        <?php if (!empty($searchTerm)): ?>
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($searchTerm); ?>">
                        <?php endif; ?>
                        <input
                            type="text"
                            name="bin_search"
                            value="<?php echo htmlspecialchars($binSearch); ?>"
                            placeholder="Ex: Visa, Mastercard, Elo, Bradesco... (busca exata)"
                            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        >
                        <button
                            type="submit"
                            class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                        >
                            Filtrar
                        </button>
                    </form>
                </div>
            </div>

            <!-- Botões de Limpeza -->
            <?php if (!empty($searchTerm) || !empty($binSearch)): ?>
                <div class="mt-4 flex gap-2">
                    <?php if (!empty($searchTerm)): ?>
                        <a href="?<?php echo !empty($binSearch) ? 'bin_search=' . urlencode($binSearch) : ''; ?>"
                           class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                            Limpar busca de cartões
                        </a>
                    <?php endif; ?>
                    <?php if (!empty($binSearch)): ?>
                        <a href="?<?php echo !empty($searchTerm) ? 'search=' . urlencode($searchTerm) : ''; ?>"
                           class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                            Limpar filtro de BINs
                        </a>
                    <?php endif; ?>
                    <a href="?" class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                        Limpar tudo
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Seleção de País -->
        <div id="country-selection" class="bg-white rounded-lg shadow-md p-6 mb-8 <?php echo $selectedBin ? 'compact-mode' : ''; ?>">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-900">🌍 Países</h2>
                <?php if ($selectedBin): ?>
                    <button onclick="clearSelection()" class="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">
                        Expandir
                    </button>
                <?php endif; ?>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                <?php
                $flags = [
                    'Brasil' => '🇧🇷',
                    'Estados Unidos' => '🇺🇸',
                    'Espanha' => '🇪🇸',
                    'Japão' => '🇯🇵',
                    'China' => '🇨🇳',
                    'Canadá' => '🇨🇦',
                    'França' => '🇫🇷',
                    'Alemanha' => '🇩🇪',
                    'Reino Unido' => '🇬🇧',
                    'Argentina' => '🇦🇷',
                    'México' => '🇲🇽'
                ];

                if (empty($binsByCountry)): ?>
                    <div class="col-span-full text-center py-8">
                        <div class="text-4xl mb-4">🔍</div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Nenhuma BIN encontrada</h3>
                        <p class="text-gray-600">
                            <?php if (!empty($binSearch)): ?>
                                Não foram encontradas BINs para o filtro "<strong><?php echo htmlspecialchars($binSearch); ?></strong>".
                                <br>Tente termos como: Visa, Mastercard, Elo, Bradesco, etc.
                            <?php else: ?>
                                Nenhuma BIN disponível no momento.
                            <?php endif; ?>
                        </p>
                    </div>
                <?php else: ?>
                    <?php foreach ($binsByCountry as $country => $bins): ?>
                        <button
                            onclick="toggleCountry('<?php echo htmlspecialchars($country); ?>')"
                            class="country-btn p-4 rounded-lg border-2 transition-all hover:shadow-md text-center"
                            data-country="<?php echo htmlspecialchars($country); ?>"
                        >
                            <div class="text-3xl mb-2"><?php echo $flags[$country] ?? '🏳️'; ?></div>
                            <div class="font-semibold text-sm text-gray-900"><?php echo htmlspecialchars($country); ?></div>
                            <div class="text-xs text-gray-600"><?php echo count($bins); ?> BINs</div>
                        </button>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- BINs do País Selecionado -->
        <?php foreach ($binsByCountry as $country => $bins): ?>
            <div id="bins-<?php echo htmlspecialchars($country); ?>" class="country-bins bg-white rounded-lg shadow-md p-6 mb-8 <?php echo $selectedBin ? 'compact-mode' : ''; ?>" style="display: none;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">
                        <?php echo $flags[$country] ?? '🏳️'; ?> BINs do <?php echo htmlspecialchars($country); ?>
                    </h2>
                    <?php if ($selectedBin): ?>
                        <button onclick="clearSelection()" class="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">
                            Expandir
                        </button>
                    <?php endif; ?>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <?php foreach ($bins as $binData): ?>
                        <a href="?bin=<?php echo urlencode($binData['bin']); ?><?php echo !empty($binSearch) ? '&bin_search=' . urlencode($binSearch) : ''; ?><?php echo !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : ''; ?>"
                           class="block p-4 rounded-lg border-2 transition-all hover:shadow-md <?php echo $selectedBin === $binData['bin'] ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'; ?>">
                            <div class="text-center">
                                <div class="font-mono font-bold text-lg text-gray-900 mb-2"><?php echo htmlspecialchars($binData['bin']); ?></div>

                                <?php if ($binData['info']): ?>
                                    <div class="text-sm font-semibold text-gray-700 mb-1">
                                        <?php echo htmlspecialchars($binData['info']['brand']); ?>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-2 truncate">
                                        <?php echo htmlspecialchars($binData['info']['issuer']); ?>
                                    </div>
                                <?php else: ?>
                                    <div class="text-sm text-gray-500 mb-2">Info não disponível</div>
                                <?php endif; ?>

                                <div class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full inline-block">
                                    <?php echo number_format($binData['total_cards']); ?> cartões
                                </div>
                            </div>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>

        <!-- Detalhes da BIN e Cartões -->
        <div class="w-full">
                <?php if ($selectedBin): ?>
                    <!-- Informações da BIN Selecionada -->
                    <?php if ($binInfo): ?>
                        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                            <h2 class="text-xl font-semibold text-gray-900 mb-4">📋 Informações da BIN <?php echo htmlspecialchars($selectedBin); ?></h2>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p><strong>Bandeira:</strong> <?php echo htmlspecialchars($binInfo['brand']); ?></p>
                                    <p><strong>Emissor:</strong> <?php echo htmlspecialchars($binInfo['issuer']); ?></p>
                                    <p><strong>Tipo:</strong> <?php echo htmlspecialchars($binInfo['type']); ?></p>
                                    <?php if (!empty($binInfo['category'])): ?>
                                        <p><strong>Categoria:</strong> <?php echo htmlspecialchars($binInfo['category']); ?></p>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <p><strong>País:</strong> <?php echo htmlspecialchars($binInfo['country']); ?> (<?php echo htmlspecialchars($binInfo['country_code']); ?>)</p>
                                    <?php if (!empty($binInfo['iso_code3'])): ?>
                                        <p><strong>ISO:</strong> <?php echo htmlspecialchars($binInfo['iso_code3']); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($binInfo['website']) && $binInfo['website'] !== ''): ?>
                                        <p><strong>Website:</strong>
                                            <a href="<?php echo htmlspecialchars($binInfo['website']); ?>"
                                               target="_blank" class="text-blue-600 hover:underline">
                                                <?php echo htmlspecialchars($binInfo['website']); ?>
                                            </a>
                                        </p>
                                    <?php endif; ?>
                                    <?php if (!empty($binInfo['phone']) && $binInfo['phone'] !== ''): ?>
                                        <p><strong>Telefone:</strong> <?php echo htmlspecialchars($binInfo['phone']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Lista de Cartões -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">
                            💳 Cartões da BIN <?php echo htmlspecialchars($selectedBin); ?>
                            <span class="text-sm font-normal text-gray-600">(<?php echo count($selectedCards); ?> cartões)</span>
                        </h2>

                        <?php if (empty($selectedCards)): ?>
                            <p class="text-gray-500 text-center py-8">Nenhum cartão encontrado para esta BIN.</p>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <th class="text-left py-2 px-3">Número</th>
                                            <th class="text-left py-2 px-3">Validade</th>
                                            <th class="text-left py-2 px-3">CVV</th>
                                            <th class="text-left py-2 px-3">Nome</th>
                                            <th class="text-left py-2 px-3">CPF</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($selectedCards as $card): ?>
                                            <tr class="border-b border-gray-100 hover:bg-gray-50">
                                                <td class="py-2 px-3 font-mono">
                                                    <div class="flex items-center gap-2">
                                                        <span><?php echo htmlspecialchars(chunk_split($card['numero'], 4, ' ')); ?></span>
                                                        <button
                                                            onclick="copyCard('<?php echo htmlspecialchars($card['numero']); ?>', '<?php echo htmlspecialchars($card['validade']); ?>', '<?php echo htmlspecialchars($card['cvv']); ?>')"
                                                            class="copy-btn text-blue-600 hover:text-blue-800 text-xs px-2 py-1 rounded border border-blue-300 hover:bg-blue-50"
                                                            title="Copiar cartão (Número|Validade|CVV)"
                                                        >
                                                            📋
                                                        </button>
                                                    </div>
                                                </td>
                                                <td class="py-2 px-3"><?php echo htmlspecialchars($card['validade']); ?></td>
                                                <td class="py-2 px-3"><?php echo htmlspecialchars($card['cvv']); ?></td>
                                                <td class="py-2 px-3"><?php echo htmlspecialchars($card['nome']); ?></td>
                                                <td class="py-2 px-3"><?php echo htmlspecialchars($card['cpf']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php elseif (!empty($searchResults)): ?>
                    <!-- Resultados da Busca -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">
                            🔍 Resultados da Busca
                            <span class="text-sm font-normal text-gray-600">(<?php echo count($searchResults); ?> resultados)</span>
                        </h2>

                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-2 px-3">Número</th>
                                        <th class="text-left py-2 px-3">Validade</th>
                                        <th class="text-left py-2 px-3">CVV</th>
                                        <th class="text-left py-2 px-3">Nome</th>
                                        <th class="text-left py-2 px-3">CPF</th>
                                        <th class="text-left py-2 px-3">BIN</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($searchResults as $card): ?>
                                        <tr class="border-b border-gray-100 hover:bg-gray-50">
                                            <td class="py-2 px-3 font-mono">
                                                <div class="flex items-center gap-2">
                                                    <span><?php echo htmlspecialchars(chunk_split($card['numero'], 4, ' ')); ?></span>
                                                    <button
                                                        onclick="copyCard('<?php echo htmlspecialchars($card['numero']); ?>', '<?php echo htmlspecialchars($card['validade']); ?>', '<?php echo htmlspecialchars($card['cvv']); ?>')"
                                                        class="copy-btn text-blue-600 hover:text-blue-800 text-xs px-2 py-1 rounded border border-blue-300 hover:bg-blue-50"
                                                        title="Copiar cartão (Número|Validade|CVV)"
                                                    >
                                                        📋
                                                    </button>
                                                </div>
                                            </td>
                                            <td class="py-2 px-3"><?php echo htmlspecialchars($card['validade']); ?></td>
                                            <td class="py-2 px-3"><?php echo htmlspecialchars($card['cvv']); ?></td>
                                            <td class="py-2 px-3"><?php echo htmlspecialchars($card['nome']); ?></td>
                                            <td class="py-2 px-3"><?php echo htmlspecialchars($card['cpf']); ?></td>
                                            <td class="py-2 px-3">
                                                <a href="?bin=<?php echo urlencode(substr($card['numero'], 0, 6)); ?><?php echo !empty($binSearch) ? '&bin_search=' . urlencode($binSearch) : ''; ?><?php echo !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : ''; ?>"
                                                   class="text-blue-600 hover:underline font-mono">
                                                    <?php echo htmlspecialchars(substr($card['numero'], 0, 6)); ?>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Estado Inicial -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="text-center py-12">
                            <div class="text-6xl mb-4">🌍</div>
                            <h2 class="text-2xl font-semibold text-gray-900 mb-2">Selecione um País</h2>
                            <p class="text-gray-600 mb-6">Clique em um país acima para ver suas BINs disponíveis</p>
                            <div class="text-sm text-gray-500">
                                Ou use a busca para encontrar cartões específicos
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>


    </div>

    <script>
        function toggleDarkMode() {
            const body = document.getElementById('body');
            const isDark = body.classList.contains('dark');
            
            if (isDark) {
                body.classList.remove('dark');
                body.classList.add('bg-gray-100');
                body.classList.remove('bg-gray-900');
                localStorage.setItem('darkMode', 'false');
            } else {
                body.classList.add('dark');
                body.classList.remove('bg-gray-100');
                body.classList.add('bg-gray-900');
                localStorage.setItem('darkMode', 'true');
            }
        }

        // Carregar preferência de modo escuro
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode');
            if (darkMode === 'true') {
                toggleDarkMode();
            }
        });

        // Controle de exibição das BINs por país
        function toggleCountry(country) {
            // Esconder todas as seções de BINs
            const allBins = document.querySelectorAll('.country-bins');
            allBins.forEach(bin => bin.style.display = 'none');

            // Remover seleção de todos os botões de país
            const allButtons = document.querySelectorAll('.country-btn');
            allButtons.forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50');
                btn.classList.add('border-gray-200');
            });

            // Mostrar BINs do país selecionado
            const selectedBins = document.getElementById('bins-' + country);
            if (selectedBins) {
                selectedBins.style.display = 'block';
                selectedBins.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }

            // Destacar botão do país selecionado
            const selectedButton = document.querySelector(`[data-country="${country}"]`);
            if (selectedButton) {
                selectedButton.classList.remove('border-gray-200');
                selectedButton.classList.add('border-blue-500', 'bg-blue-50');
            }
        }

        // Limpar seleção e expandir menus
        function clearSelection() {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('bin');
            window.location.href = currentUrl.toString();
        }

        // Copiar dados do cartão
        function copyCard(numero, validade, cvv) {
            const cardData = `${numero}|${validade}|${cvv}`;

            // Tentar usar a API moderna de clipboard
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(cardData).then(() => {
                    showCopyNotification('Cartão copiado!');
                }).catch(() => {
                    fallbackCopyTextToClipboard(cardData);
                });
            } else {
                fallbackCopyTextToClipboard(cardData);
            }
        }

        // Fallback para navegadores mais antigos
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopyNotification('Cartão copiado!');
            } catch (err) {
                showCopyNotification('Erro ao copiar', true);
            }

            document.body.removeChild(textArea);
        }

        // Mostrar notificação de cópia
        function showCopyNotification(message, isError = false) {
            // Remover notificação existente
            const existing = document.getElementById('copy-notification');
            if (existing) {
                existing.remove();
            }

            // Criar nova notificação
            const notification = document.createElement('div');
            notification.id = 'copy-notification';
            notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 transition-all duration-300 ${isError ? 'bg-red-500' : 'bg-green-500'}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Remover após 3 segundos
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Auto-selecionar país se uma BIN estiver selecionada
        document.addEventListener('DOMContentLoaded', function() {
            <?php if ($selectedBin): ?>
                // Encontrar o país da BIN selecionada
                <?php
                $selectedCountry = null;
                foreach ($binsByCountry as $country => $bins) {
                    foreach ($bins as $binData) {
                        if ($binData['bin'] === $selectedBin) {
                            $selectedCountry = $country;
                            break 2;
                        }
                    }
                }
                ?>
                <?php if ($selectedCountry): ?>
                    toggleCountry('<?php echo htmlspecialchars($selectedCountry); ?>');
                <?php endif; ?>
            <?php endif; ?>
        });
    </script>
</body>
</html>
