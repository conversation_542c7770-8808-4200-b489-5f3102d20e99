<?php
require_once 'card_checker.php';

$checker = new CardChecker();
$error = null;
$success = null;
$selectedBin = $_GET['bin'] ?? null;
$searchTerm = $_GET['search'] ?? '';

try {
    $stats = $checker->getDatabaseStats();
    $uniqueBins = $checker->getUniqueBinsFromDatabase();

    $selectedCards = [];
    $binInfo = null;

    if ($selectedBin) {
        $selectedCards = $checker->getCardsByBin($selectedBin);
        $binInfo = $checker->getBinInfo($selectedBin . '000000'); // Adiciona zeros para buscar info
    }

    $searchResults = [];
    if (!empty($searchTerm)) {
        $searchResults = $checker->searchCards($searchTerm);
    }

} catch (Exception $e) {
    $error = "Erro: " . $e->getMessage();
    $stats = ['total_cards' => 0, 'unique_bins' => 0, 'origins' => []];
    $uniqueBins = [];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificador de Cartões e BINs</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .dark {
            background-color: #1a202c;
            color: #e2e8f0;
        }
        .dark .bg-white {
            background-color: #2d3748;
        }
        .dark .text-gray-900 {
            color: #e2e8f0;
        }
        .dark .border-gray-300 {
            border-color: #4a5568;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen" id="body">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Verificador de Cartões por BIN</h1>
                <p class="text-gray-600 mt-2">Selecione uma BIN para visualizar os cartões associados</p>
            </div>
            <button onclick="toggleDarkMode()" class="bg-gray-800 text-white px-4 py-2 rounded hover:bg-gray-700">
                🌙 Modo Escuro
            </button>
        </div>

        <!-- Estatísticas -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-2xl font-bold text-blue-600"><?php echo number_format($stats['total_cards']); ?></h3>
                <p class="text-gray-600">Total de Cartões</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-2xl font-bold text-green-600"><?php echo number_format($stats['unique_bins']); ?></h3>
                <p class="text-gray-600">BINs Únicas</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 class="text-2xl font-bold text-purple-600"><?php echo count($stats['origins']); ?></h3>
                <p class="text-gray-600">Arquivos de Origem</p>
            </div>
        </div>

        <!-- Mensagens -->
        <?php if ($error): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <!-- Busca -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">🔍 Buscar Cartões</h2>
            <form method="GET" class="flex gap-4">
                <input
                    type="text"
                    name="search"
                    value="<?php echo htmlspecialchars($searchTerm); ?>"
                    placeholder="Buscar por número, nome ou CPF..."
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <button
                    type="submit"
                    class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                    Buscar
                </button>
                <?php if (!empty($searchTerm)): ?>
                    <a href="?" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                        Limpar
                    </a>
                <?php endif; ?>
            </form>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Lista de BINs -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6 sticky top-4">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">📊 BINs Disponíveis</h2>

                    <div class="space-y-2 max-h-96 overflow-y-auto">
                        <?php foreach ($uniqueBins as $binData): ?>
                            <a href="?bin=<?php echo urlencode($binData['bin']); ?>"
                               class="block p-3 rounded-lg border transition-colors <?php echo $selectedBin === $binData['bin'] ? 'bg-blue-100 border-blue-500' : 'hover:bg-gray-50 border-gray-200'; ?>">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <div class="font-mono font-semibold text-lg"><?php echo htmlspecialchars($binData['bin']); ?></div>
                                        <?php if ($binData['info']): ?>
                                            <div class="text-sm text-gray-600">
                                                <?php echo htmlspecialchars($binData['info']['brand']); ?> -
                                                <?php echo htmlspecialchars($binData['info']['country']); ?>
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                <?php echo htmlspecialchars($binData['info']['issuer']); ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-sm text-gray-500">Informações não disponíveis</div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-right">
                                        <div class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                            <?php echo number_format($binData['total_cards']); ?> cartões
                                        </div>
                                    </div>
                                </div>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Detalhes da BIN e Cartões -->
            <div class="lg:col-span-2">
                <?php if ($selectedBin): ?>
                    <!-- Informações da BIN Selecionada -->
                    <?php if ($binInfo): ?>
                        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                            <h2 class="text-xl font-semibold text-gray-900 mb-4">📋 Informações da BIN <?php echo htmlspecialchars($selectedBin); ?></h2>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p><strong>Bandeira:</strong> <?php echo htmlspecialchars($binInfo['brand']); ?></p>
                                    <p><strong>Emissor:</strong> <?php echo htmlspecialchars($binInfo['issuer']); ?></p>
                                    <p><strong>Tipo:</strong> <?php echo htmlspecialchars($binInfo['type']); ?></p>
                                </div>
                                <div>
                                    <p><strong>País:</strong> <?php echo htmlspecialchars($binInfo['country']); ?> (<?php echo htmlspecialchars($binInfo['country_code']); ?>)</p>
                                    <?php if (!empty($binInfo['website'])): ?>
                                        <p><strong>Website:</strong>
                                            <a href="<?php echo htmlspecialchars($binInfo['website']); ?>"
                                               target="_blank" class="text-blue-600 hover:underline">
                                                <?php echo htmlspecialchars($binInfo['website']); ?>
                                            </a>
                                        </p>
                                    <?php endif; ?>
                                    <?php if (!empty($binInfo['phone'])): ?>
                                        <p><strong>Telefone:</strong> <?php echo htmlspecialchars($binInfo['phone']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Lista de Cartões -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">
                            💳 Cartões da BIN <?php echo htmlspecialchars($selectedBin); ?>
                            <span class="text-sm font-normal text-gray-600">(<?php echo count($selectedCards); ?> cartões)</span>
                        </h2>

                        <?php if (empty($selectedCards)): ?>
                            <p class="text-gray-500 text-center py-8">Nenhum cartão encontrado para esta BIN.</p>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <th class="text-left py-2 px-3">Número</th>
                                            <th class="text-left py-2 px-3">Validade</th>
                                            <th class="text-left py-2 px-3">CVV</th>
                                            <th class="text-left py-2 px-3">Nome</th>
                                            <th class="text-left py-2 px-3">CPF</th>
                                            <th class="text-left py-2 px-3">Origem</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($selectedCards as $card): ?>
                                            <tr class="border-b border-gray-100 hover:bg-gray-50">
                                                <td class="py-2 px-3 font-mono"><?php echo htmlspecialchars(chunk_split($card['numero'], 4, ' ')); ?></td>
                                                <td class="py-2 px-3"><?php echo htmlspecialchars($card['validade']); ?></td>
                                                <td class="py-2 px-3"><?php echo htmlspecialchars($card['cvv']); ?></td>
                                                <td class="py-2 px-3"><?php echo htmlspecialchars($card['nome']); ?></td>
                                                <td class="py-2 px-3"><?php echo htmlspecialchars($card['cpf']); ?></td>
                                                <td class="py-2 px-3 text-xs text-gray-600"><?php echo htmlspecialchars($card['origem_arquivo']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php elseif (!empty($searchResults)): ?>
                    <!-- Resultados da Busca -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">
                            🔍 Resultados da Busca
                            <span class="text-sm font-normal text-gray-600">(<?php echo count($searchResults); ?> resultados)</span>
                        </h2>

                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-2 px-3">Número</th>
                                        <th class="text-left py-2 px-3">Validade</th>
                                        <th class="text-left py-2 px-3">CVV</th>
                                        <th class="text-left py-2 px-3">Nome</th>
                                        <th class="text-left py-2 px-3">CPF</th>
                                        <th class="text-left py-2 px-3">BIN</th>
                                        <th class="text-left py-2 px-3">Origem</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($searchResults as $card): ?>
                                        <tr class="border-b border-gray-100 hover:bg-gray-50">
                                            <td class="py-2 px-3 font-mono"><?php echo htmlspecialchars(chunk_split($card['numero'], 4, ' ')); ?></td>
                                            <td class="py-2 px-3"><?php echo htmlspecialchars($card['validade']); ?></td>
                                            <td class="py-2 px-3"><?php echo htmlspecialchars($card['cvv']); ?></td>
                                            <td class="py-2 px-3"><?php echo htmlspecialchars($card['nome']); ?></td>
                                            <td class="py-2 px-3"><?php echo htmlspecialchars($card['cpf']); ?></td>
                                            <td class="py-2 px-3">
                                                <a href="?bin=<?php echo urlencode(substr($card['numero'], 0, 6)); ?>"
                                                   class="text-blue-600 hover:underline font-mono">
                                                    <?php echo htmlspecialchars(substr($card['numero'], 0, 6)); ?>
                                                </a>
                                            </td>
                                            <td class="py-2 px-3 text-xs text-gray-600"><?php echo htmlspecialchars($card['origem_arquivo']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Estado Inicial -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="text-center py-12">
                            <div class="text-6xl mb-4">🏦</div>
                            <h2 class="text-2xl font-semibold text-gray-900 mb-2">Selecione uma BIN</h2>
                            <p class="text-gray-600 mb-6">Escolha uma BIN na lista ao lado para visualizar os cartões associados</p>
                            <div class="text-sm text-gray-500">
                                Ou use a busca acima para encontrar cartões específicos
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Arquivos de Origem -->
        <?php if (!empty($stats['origins'])): ?>
            <div class="mt-8 bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">📁 Arquivos de Origem</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php foreach ($stats['origins'] as $origin): ?>
                        <div class="bg-gray-50 p-4 rounded-lg border">
                            <div class="font-semibold text-gray-900"><?php echo htmlspecialchars($origin['origem_arquivo']); ?></div>
                            <div class="text-sm text-gray-600"><?php echo number_format($origin['total']); ?> cartões</div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function toggleDarkMode() {
            const body = document.getElementById('body');
            const isDark = body.classList.contains('dark');
            
            if (isDark) {
                body.classList.remove('dark');
                body.classList.add('bg-gray-100');
                body.classList.remove('bg-gray-900');
                localStorage.setItem('darkMode', 'false');
            } else {
                body.classList.add('dark');
                body.classList.remove('bg-gray-100');
                body.classList.add('bg-gray-900');
                localStorage.setItem('darkMode', 'true');
            }
        }

        // Carregar preferência de modo escuro
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode');
            if (darkMode === 'true') {
                toggleDarkMode();
            }
        });

        // Scroll suave para BINs selecionadas
        document.addEventListener('DOMContentLoaded', function() {
            const selectedBin = document.querySelector('.bg-blue-100');
            if (selectedBin) {
                selectedBin.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    </script>
</body>
</html>
