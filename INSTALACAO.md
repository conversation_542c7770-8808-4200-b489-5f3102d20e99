# Guia de Instalação e Uso

## Instalação do PHP

### Windows

1. **Baixar PHP**:
   - Acesse: https://windows.php.net/download/
   - Baixe a versão "Thread Safe" mais recente
   - <PERSON>ia para `C:\php`

2. **Configurar PATH**:
   - Adicione `C:\php` ao PATH do sistema
   - Ou use XAMPP/WAMP que já inclui PHP

3. **Verificar instalação**:
   ```cmd
   php -v
   ```

### Linux/Mac

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install php

# CentOS/RHEL
sudo yum install php

# Mac (com Homebrew)
brew install php
```

## Como Executar

### 1. Teste via Linha de Comando
```bash
php test_checker.php
```

### 2. Servidor Web Local
```bash
# Iniciar servidor PHP embutido
php -S localhost:8000

# Acessar no navegador
http://localhost:8000
```

### 3. Com XAMPP/WAMP
1. Copie os arquivos para a pasta `htdocs` (XAMPP) ou `www` (WAMP)
2. Acesse: `http://localhost/nome-da-pasta`

## Exemplo de Saída Esperada

```
=== VERIFICADOR DE CARTÕES E BINS ===

Testando cartão: ****************
--------------------------------------------------
Número formatado: 4111 1111 1111 1111 
Válido (Luhn): SIM
Bandeira: VISA
Na lista: SIM
Informações da BIN:
  - BIN: 411111
  - Emissor: CHASE BANK USA, N.A.
  - Tipo: CREDIT
  - País: UNITED STATES
  - Website: http://www.chase.com/

Testando cartão: ****************
--------------------------------------------------
Número formatado: 4242 4242 4242 4242 
Válido (Luhn): SIM
Bandeira: VISA
Na lista: SIM
BIN não encontrada na base de dados.

=== ESTATÍSTICAS ===
Total de BINs carregadas: 21
Total de cartões na lista: 21
```

## Estrutura dos Arquivos

```
Project/
├── card_checker.php    # Classe principal
├── index.php          # Interface web
├── test_checker.php   # Teste linha de comando
├── bins.csv           # Base de dados BINs
├── cards.txt          # Lista de cartões
├── README.md          # Documentação
└── INSTALACAO.md      # Este arquivo
```

## Troubleshooting

### Erro: "Class 'CardChecker' not found"
- Verifique se o arquivo `card_checker.php` está no mesmo diretório
- Certifique-se de que o `require_once` está correto

### Erro: "Permission denied"
- No Linux/Mac: `chmod 755 *.php`
- Verifique permissões de escrita para `cards.txt`

### Interface web não carrega
- Verifique se o servidor web está rodando
- Confirme se o PHP está habilitado no servidor
- Teste com: `php -S localhost:8000`

### BINs não são encontradas
- Verifique se o arquivo `bins.csv` existe
- Confirme o formato do CSV (separado por ponto e vírgula)
- Teste com BINs conhecidas: 411111, 424242, 520000

## Personalização

### Adicionar novas BINs
Edite o arquivo `bins.csv` seguindo o formato:
```
BIN;BRAND;ISSUER;TYPE;LEVEL;COUNTRY;COUNTRY_CODE;WEBSITE;PHONE
```

### Modificar validações
Edite os métodos na classe `CardChecker`:
- `isValidCard()` - Algoritmo de Luhn
- `identifyBrand()` - Padrões de bandeiras
- `getBinInfo()` - Busca de BINs

### Customizar interface
Modifique `index.php`:
- Classes CSS do Tailwind
- JavaScript para interatividade
- Formulários e validações
