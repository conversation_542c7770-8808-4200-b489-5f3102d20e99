<?php
require_once 'card_checker.php';

echo "=== VERIFICADOR DE CARTÕES E BINS ===\n\n";

try {
    $checker = new CardChecker();
    
    // Cartões de teste
    $testCards = [
        '****************',  // Visa válido
        '****************',  // Visa válido
        '****************',  // Mastercard válido
        '3400000000000000',  // Amex válido
        '1800030000000000',  // JCB válido
        '1234567890123456',  // Inválido
        '****************'   // Visa inválido (falha <PERSON>)
    ];
    
    foreach ($testCards as $card) {
        echo "Testando cartão: $card\n";
        echo str_repeat("-", 50) . "\n";
        
        $result = $checker->analyzeCard($card);
        
        echo "Número formatado: " . $result['formatted_number'] . "\n";
        echo "Válido (Luhn): " . ($result['is_valid'] ? 'SIM' : 'NÃO') . "\n";
        echo "Bandeira: " . $result['brand'] . "\n";
        echo "Na lista: " . ($result['in_list'] ? 'SIM' : 'NÃO') . "\n";
        
        if ($result['bin_info']) {
            echo "Informações da BIN:\n";
            echo "  - BIN: " . $result['bin_info']['bin'] . "\n";
            echo "  - Emissor: " . $result['bin_info']['issuer'] . "\n";
            echo "  - Tipo: " . $result['bin_info']['type'] . "\n";
            echo "  - País: " . $result['bin_info']['country'] . "\n";
            if (!empty($result['bin_info']['website'])) {
                echo "  - Website: " . $result['bin_info']['website'] . "\n";
            }
        } else {
            echo "BIN não encontrada na base de dados.\n";
        }
        
        echo "\n";
    }
    
    // Estatísticas
    echo "=== ESTATÍSTICAS ===\n";
    echo "Total de BINs carregadas: " . count($checker->getAllBins()) . "\n";
    echo "Total de cartões na lista: " . count($checker->getAllCards()) . "\n";
    
    echo "\n=== TESTE DE ADIÇÃO/REMOÇÃO ===\n";
    
    // Teste de adição
    $newCard = '****************';
    echo "Adicionando cartão: $newCard\n";
    if ($checker->addCard($newCard)) {
        echo "Cartão adicionado com sucesso!\n";
        $checker->saveCards();
        echo "Lista salva no arquivo.\n";
    } else {
        echo "Cartão já existe na lista.\n";
    }
    
    // Verificar se foi adicionado
    echo "Verificando se o cartão está na lista: " . ($checker->isCardInList($newCard) ? 'SIM' : 'NÃO') . "\n";
    
    // Teste de remoção
    echo "Removendo cartão: $newCard\n";
    if ($checker->removeCard($newCard)) {
        echo "Cartão removido com sucesso!\n";
        $checker->saveCards();
        echo "Lista salva no arquivo.\n";
    } else {
        echo "Cartão não encontrado na lista.\n";
    }
    
    // Verificar se foi removido
    echo "Verificando se o cartão ainda está na lista: " . ($checker->isCardInList($newCard) ? 'SIM' : 'NÃO') . "\n";
    
} catch (Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
}

echo "\n=== TESTE CONCLUÍDO ===\n";
?>
