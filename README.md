# Verificador de Cartões por BIN

Sistema PHP para visualização e análise de cartões de crédito organizados por BINs (Bank Identification Numbers) com interface BIN-first.

## Funcionalidades

- ✅ **Interface BIN-first**: Selecione BINs para visualizar cartões associados
- ✅ **Organização por país**: BINs agrupadas por Brasil, Estados Unidos, etc.
- ✅ **Banco de dados SQLite**: Armazenamento eficiente dos cartões
- ✅ **Busca avançada**: Pesquise por número, nome ou CPF
- ✅ **Informações detalhadas**: Dados completos da BIN (emissor, país, etc.)
- ✅ **Estatísticas**: Visão geral dos dados disponíveis
- ✅ **Interface moderna**: Design responsivo com modo escuro
- ✅ **Validação de cartões**: Algoritmo de Luhn integrado

## Arquivos do Sistema

- `card_checker.php` - Classe principal com toda a lógica
- `index.php` - Interface web BIN-first
- `test_checker.php` - Script de teste via linha de comando
- `test_database.php` - Teste específico do banco SQLite
- `bins.csv` - Base de dados das BINs
- `cards.db` - Banco SQLite com os cartões
- `cards.txt` - Lista de cartões (compatibilidade)

## Estrutura do Banco SQLite

```sql
CREATE TABLE cards (
    numero TEXT PRIMARY KEY,
    validade TEXT,
    cvv TEXT,
    nome TEXT,
    cpf TEXT,
    origem_arquivo TEXT
);
```

## Formato do arquivo BINs (CSV)

```
BIN;BRAND;ISSUER;TYPE;LEVEL;COUNTRY;COUNTRY_CODE;WEBSITE;PHONE
180003;JCB;JCB CO., LTD.;CREDIT;;JAPAN;JP;http://www.jcbcorporate.com/;81 3 5778 5483
```

## Como Usar

### Interface Web

1. Inicie o servidor: `php -S localhost:8000`
2. Acesse: `http://localhost:8000`
3. **Selecione uma BIN** na lista à esquerda
4. **Visualize os cartões** associados à BIN
5. Use a **busca** para encontrar cartões específicos

### Teste do Banco

```bash
php test_database.php
```

### Linha de Comando

```bash
php test_checker.php
```

### Programaticamente

```php
<?php
require_once 'card_checker.php';

$checker = new CardChecker();

// Verificar um cartão
$result = $checker->analyzeCard('****************');

echo "Válido: " . ($result['is_valid'] ? 'Sim' : 'Não') . "\n";
echo "Bandeira: " . $result['brand'] . "\n";
echo "Na lista: " . ($result['in_list'] ? 'Sim' : 'Não') . "\n";

if ($result['bin_info']) {
    echo "Emissor: " . $result['bin_info']['issuer'] . "\n";
}
?>
```

## Métodos da Classe CardChecker

### Verificação
- `analyzeCard($cardNumber)` - Análise completa do cartão
- `isValidCard($cardNumber)` - Verifica validade (Luhn)
- `identifyBrand($cardNumber)` - Identifica a bandeira
- `getBinInfo($cardNumber)` - Busca informações da BIN
- `isCardInList($cardNumber)` - Verifica se está na lista

### Banco de Dados
- `getUniqueBinsFromDatabase()` - Lista BINs organizadas por país
- `getCardsByBin($bin)` - Busca cartões por BIN específica
- `getDatabaseStats()` - Estatísticas do banco (total, BINs, países)
- `searchCards($term)` - Busca cartões por termo
- `translateCountry($country)` - Traduz nomes de países

### Gerenciamento (Compatibilidade)
- `addCard($cardNumber)` - Adiciona cartão à lista
- `removeCard($cardNumber)` - Remove cartão da lista
- `saveCards($filename)` - Salva lista no arquivo
- `getAllCards()` - Retorna todos os cartões
- `getAllBins()` - Retorna todas as BINs

## Bandeiras Suportadas

- VISA (inicia com 4)
- MASTERCARD (inicia com 5 ou 2)
- AMERICAN EXPRESS (inicia com 34 ou 37)
- DISCOVER (inicia com 6011 ou 65)
- JCB (inicia com 2131, 1800 ou 35)
- UNIONPAY (inicia com 62)

## Requisitos

- PHP 7.0 ou superior
- Servidor web (Apache, Nginx) para interface web
- Permissões de escrita para salvar alterações na lista

## Estrutura de Resposta

```php
[
    'card_number' => '****************',
    'formatted_number' => '4111 1111 1111 1111',
    'is_valid' => true,
    'brand' => 'VISA',
    'in_list' => true,
    'bin_info' => [
        'bin' => '411111',
        'brand' => 'VISA',
        'issuer' => 'CHASE BANK USA, N.A.',
        'type' => 'CREDIT',
        'country' => 'UNITED STATES',
        'country_code' => 'US',
        'website' => 'http://www.chase.com/',
        'phone' => '**************'
    ]
]
```

## Estrutura da Interface

### 🔍 Layout Organizado por País:

- **Coluna esquerda**: BINs organizadas por país (🇧🇷 Brasil, 🇺🇸 Estados Unidos, etc.)
- **Coluna direita**: Cartões da BIN selecionada ou resultados da busca
- **Topo**: Estatísticas gerais (cartões, BINs, países) e busca
- **Bandeiras**: Cada país tem sua bandeira para fácil identificação
- **Priorização**: Brasil e Estados Unidos aparecem primeiro na lista

## Segurança

⚠️ **Importante**: Este sistema é para fins educacionais e de teste. Para uso em produção:

- Implemente validação adicional de entrada
- Use HTTPS para transmissão de dados
- Considere criptografia para armazenamento
- Implemente logs de auditoria
- Valide permissões de acesso

## Licença

Este projeto é fornecido como exemplo educacional. Use por sua própria conta e risco.
