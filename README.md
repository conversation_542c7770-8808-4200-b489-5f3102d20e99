# Verificador de Cartões por BIN

Sistema PHP para visualização e análise de cartões de crédito organizados por BINs (Bank Identification Numbers) com interface BIN-first.

## Funcionalidades

- ✅ **Interface BIN-first**: Selecione BINs para visualizar cartões associados
- ✅ **Organização por país**: BINs agrupadas por Brasil, Estados Unidos, etc.
- ✅ **Busca dupla**: Busca de cartões + filtro de BINs por bandeira/emissor
- ✅ **Interface compacta**: Menus se reduzem quando BIN selecionada
- ✅ **Copiar cartões**: Botão para copiar dados do cartão (Número|Validade|CVV)
- ✅ **Banco de dados SQLite**: Armazenamento eficiente dos cartões
- ✅ **Informações detalhadas**: Dados completos da BIN (emissor, país, etc.)
- ✅ **Estatísticas**: Visão geral dos dados disponíveis
- ✅ **Interface moderna**: Design responsivo com modo escuro
- ✅ **Validação de cartões**: Algoritmo de Luhn integrado

## Arquivos do Sistema

- `card_checker.php` - Classe principal com toda a lógica
- `index.php` - Interface web BIN-first
- `bins.csv` - Base de dados das BINs
- `cards.db` - Banco SQLite com os cartões
- `README.md` - Documentação completa

## Estrutura do Banco SQLite

```sql
CREATE TABLE cards (
    numero TEXT PRIMARY KEY,
    validade TEXT,
    cvv TEXT,
    nome TEXT,
    cpf TEXT,
    origem_arquivo TEXT
);
```

## Formato do arquivo BINs (CSV)

```
BIN,Brand,Type,Category,Issuer,IssuerPhone,IssuerUrl,isoCode2,isoCode3,CountryName
002102,"PRIVATE LABEL",CREDIT,STANDARD,"CHINA MERCHANTS BANK",95555,https://english.cmbchina.com,CN,CHN,CHINA
007343,"PRIVATE LABEL",DEBIT,,"BANCO PAN",+************,,BR,BRA,BRAZIL
```

**Campos:**
- **BIN**: Número identificador do banco (6 dígitos)
- **Brand**: Bandeira do cartão (VISA, MASTERCARD, etc.)
- **Type**: Tipo do cartão (CREDIT, DEBIT)
- **Category**: Categoria específica (STANDARD, GIFT, etc.)
- **Issuer**: Nome do banco emissor
- **IssuerPhone**: Telefone do emissor
- **IssuerUrl**: Website do emissor
- **isoCode2**: Código ISO de 2 letras do país
- **isoCode3**: Código ISO de 3 letras do país
- **CountryName**: Nome do país em inglês

## Como Usar

### Interface Web

1. Inicie o servidor: `php -S localhost:8000`
2. Acesse: `http://localhost:8000`
3. **Use os filtros**: Busque cartões ou filtre BINs por bandeira (ex: "Visa", "Mastercard")
4. **Clique em um país** nos botões horizontais (ex: 🇧🇷 Brasil)
5. **Selecione uma BIN** no grid que aparece
6. **Visualize os cartões** da BIN selecionada (interface compacta)
7. **Expanda** clicando em "Expandir" para voltar à navegação

### Verificação do Sistema

Para verificar se tudo está funcionando, acesse a interface web e:
1. Verifique se as estatísticas aparecem corretamente
2. Teste a seleção de países e BINs
3. Experimente as funcionalidades de busca e filtro

### Programaticamente

```php
<?php
require_once 'card_checker.php';

$checker = new CardChecker();

// Verificar um cartão
$result = $checker->analyzeCard('****************');

echo "Válido: " . ($result['is_valid'] ? 'Sim' : 'Não') . "\n";
echo "Bandeira: " . $result['brand'] . "\n";
echo "Na lista: " . ($result['in_list'] ? 'Sim' : 'Não') . "\n";

if ($result['bin_info']) {
    echo "Emissor: " . $result['bin_info']['issuer'] . "\n";
}
?>
```

## Métodos da Classe CardChecker

### Verificação
- `analyzeCard($cardNumber)` - Análise completa do cartão
- `isValidCard($cardNumber)` - Verifica validade (Luhn)
- `identifyBrand($cardNumber)` - Identifica a bandeira
- `getBinInfo($cardNumber)` - Busca informações da BIN
- `isCardInList($cardNumber)` - Verifica se está na lista

### Banco de Dados
- `getUniqueBinsFromDatabase()` - Lista BINs organizadas por país
- `getCardsByBin($bin)` - Busca cartões por BIN específica
- `getDatabaseStats()` - Estatísticas do banco (total, BINs, países)
- `searchCards($term)` - Busca cartões por termo
- `filterBinsBySearch($term, $bins)` - Filtra BINs por bandeira/emissor
- `translateCountry($country)` - Traduz nomes de países

### Gerenciamento (Compatibilidade)
- `addCard($cardNumber)` - Adiciona cartão à lista
- `removeCard($cardNumber)` - Remove cartão da lista
- `saveCards($filename)` - Salva lista no arquivo
- `getAllCards()` - Retorna todos os cartões
- `getAllBins()` - Retorna todas as BINs

## Bandeiras Suportadas

- VISA (inicia com 4)
- MASTERCARD (inicia com 5 ou 2)
- AMERICAN EXPRESS (inicia com 34 ou 37)
- DISCOVER (inicia com 6011 ou 65)
- JCB (inicia com 2131, 1800 ou 35)
- UNIONPAY (inicia com 62)

## Requisitos

- PHP 7.0 ou superior
- Servidor web (Apache, Nginx) para interface web
- Permissões de escrita para salvar alterações na lista

## Estrutura de Resposta

```php
[
    'card_number' => '****************',
    'formatted_number' => '4111 1111 1111 1111',
    'is_valid' => true,
    'brand' => 'VISA',
    'in_list' => true,
    'bin_info' => [
        'bin' => '411111',
        'brand' => 'VISA',
        'issuer' => 'CHASE BANK USA, N.A.',
        'type' => 'CREDIT',
        'country' => 'UNITED STATES',
        'country_code' => 'US',
        'website' => 'http://www.chase.com/',
        'phone' => '**************'
    ]
]
```

## Estrutura da Interface

### 🔍 Layout Horizontal por País:

- **Seleção de países**: Botões horizontais com bandeiras (🇧🇷 Brasil, 🇺🇸 Estados Unidos, etc.)
- **BINs do país**: Grid horizontal das BINs quando país é selecionado
- **Cartões da BIN**: Lista completa dos cartões da BIN selecionada
- **Busca global**: Pesquisa em todos os cartões independente do país
- **Animações**: Transições suaves entre seleções
- **Priorização**: Brasil e Estados Unidos aparecem primeiro

## Segurança

⚠️ **Importante**: Este sistema é para fins educacionais e de teste. Para uso em produção:

- Implemente validação adicional de entrada
- Use HTTPS para transmissão de dados
- Considere criptografia para armazenamento
- Implemente logs de auditoria
- Valide permissões de acesso

## Licença

Este projeto é fornecido como exemplo educacional. Use por sua própria conta e risco.
