# Verificador de Cartões e BINs

Sistema PHP para verificação de cartões de crédito e suas respectivas BINs (Bank Identification Numbers).

## Funcionalidades

- ✅ Verificação de validade de cartões usando algoritmo de Luhn
- ✅ Identificação automática da bandeira do cartão
- ✅ Consulta de informações da BIN em arquivo CSV
- ✅ Gerenciamento de lista de cartões (adicionar/remover)
- ✅ Interface web com modo escuro
- ✅ Teste via linha de comando

## Arquivos do Sistema

- `card_checker.php` - Classe principal com toda a lógica
- `index.php` - Interface web para teste
- `test_checker.php` - Script de teste via linha de comando
- `bins.csv` - Base de dados das BINs
- `cards.txt` - Lista de cartões para verificação

## Formato do arquivo BINs (CSV)

```
BIN;BRAND;ISSUER;TYPE;LEVEL;COUNTRY;COUNTRY_CODE;WEBSITE;PHONE
180003;JCB;JCB CO., LTD.;CREDIT;;JAPAN;JP;http://www.jcbcorporate.com/;81 3 5778 5483
```

## Como Usar

### Interface Web

1. Abra `index.php` no navegador
2. Digite o número do cartão para verificar
3. Use as opções para adicionar/remover cartões da lista

### Linha de Comando

```bash
php test_checker.php
```

### Programaticamente

```php
<?php
require_once 'card_checker.php';

$checker = new CardChecker();

// Verificar um cartão
$result = $checker->analyzeCard('****************');

echo "Válido: " . ($result['is_valid'] ? 'Sim' : 'Não') . "\n";
echo "Bandeira: " . $result['brand'] . "\n";
echo "Na lista: " . ($result['in_list'] ? 'Sim' : 'Não') . "\n";

if ($result['bin_info']) {
    echo "Emissor: " . $result['bin_info']['issuer'] . "\n";
}
?>
```

## Métodos da Classe CardChecker

### Verificação
- `analyzeCard($cardNumber)` - Análise completa do cartão
- `isValidCard($cardNumber)` - Verifica validade (Luhn)
- `identifyBrand($cardNumber)` - Identifica a bandeira
- `getBinInfo($cardNumber)` - Busca informações da BIN
- `isCardInList($cardNumber)` - Verifica se está na lista

### Gerenciamento
- `addCard($cardNumber)` - Adiciona cartão à lista
- `removeCard($cardNumber)` - Remove cartão da lista
- `saveCards($filename)` - Salva lista no arquivo
- `getAllCards()` - Retorna todos os cartões
- `getAllBins()` - Retorna todas as BINs

## Bandeiras Suportadas

- VISA (inicia com 4)
- MASTERCARD (inicia com 5 ou 2)
- AMERICAN EXPRESS (inicia com 34 ou 37)
- DISCOVER (inicia com 6011 ou 65)
- JCB (inicia com 2131, 1800 ou 35)
- UNIONPAY (inicia com 62)

## Requisitos

- PHP 7.0 ou superior
- Servidor web (Apache, Nginx) para interface web
- Permissões de escrita para salvar alterações na lista

## Estrutura de Resposta

```php
[
    'card_number' => '****************',
    'formatted_number' => '4111 1111 1111 1111',
    'is_valid' => true,
    'brand' => 'VISA',
    'in_list' => true,
    'bin_info' => [
        'bin' => '411111',
        'brand' => 'VISA',
        'issuer' => 'CHASE BANK USA, N.A.',
        'type' => 'CREDIT',
        'country' => 'UNITED STATES',
        'country_code' => 'US',
        'website' => 'http://www.chase.com/',
        'phone' => '**************'
    ]
]
```

## Segurança

⚠️ **Importante**: Este sistema é para fins educacionais e de teste. Para uso em produção:

- Implemente validação adicional de entrada
- Use HTTPS para transmissão de dados
- Considere criptografia para armazenamento
- Implemente logs de auditoria
- Valide permissões de acesso

## Licença

Este projeto é fornecido como exemplo educacional. Use por sua própria conta e risco.
