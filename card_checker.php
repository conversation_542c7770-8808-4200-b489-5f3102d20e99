<?php

class CardChecker {
    private $bins = [];
    private $cards = [];
    
    public function __construct($binsFile = 'bins.csv', $cardsFile = 'cards.txt') {
        $this->loadBins($binsFile);
        $this->loadCards($cardsFile);
    }
    
    /**
     * Carrega as BINs do arquivo CSV
     */
    private function loadBins($filename) {
        if (!file_exists($filename)) {
            throw new Exception("Arquivo de BINs não encontrado: $filename");
        }
        
        $handle = fopen($filename, 'r');
        if ($handle !== false) {
            while (($line = fgets($handle)) !== false) {
                $data = explode(';', trim($line));
                if (count($data) >= 9) {
                    $this->bins[$data[0]] = [
                        'bin' => $data[0],
                        'brand' => $data[1],
                        'issuer' => $data[2],
                        'type' => $data[3],
                        'level' => $data[4],
                        'country' => $data[5],
                        'country_code' => $data[6],
                        'website' => $data[7],
                        'phone' => $data[8]
                    ];
                }
            }
            fclose($handle);
        }
    }
    
    /**
     * Carrega a lista de cartões do arquivo
     */
    private function loadCards($filename) {
        if (!file_exists($filename)) {
            throw new Exception("Arquivo de cartões não encontrado: $filename");
        }
        
        $this->cards = array_filter(array_map('trim', file($filename)));
    }
    
    /**
     * Verifica se um cartão é válido usando o algoritmo de Luhn
     */
    public function isValidCard($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        if (strlen($cardNumber) < 13 || strlen($cardNumber) > 19) {
            return false;
        }
        
        $sum = 0;
        $alternate = false;
        
        for ($i = strlen($cardNumber) - 1; $i >= 0; $i--) {
            $digit = intval($cardNumber[$i]);
            
            if ($alternate) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit = ($digit % 10) + 1;
                }
            }
            
            $sum += $digit;
            $alternate = !$alternate;
        }
        
        return ($sum % 10) === 0;
    }
    
    /**
     * Identifica a bandeira do cartão baseado no número
     */
    public function identifyBrand($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        // Visa
        if (preg_match('/^4/', $cardNumber)) {
            return 'VISA';
        }
        
        // Mastercard
        if (preg_match('/^5[1-5]/', $cardNumber) || preg_match('/^2[2-7]/', $cardNumber)) {
            return 'MASTERCARD';
        }
        
        // American Express
        if (preg_match('/^3[47]/', $cardNumber)) {
            return 'AMERICAN EXPRESS';
        }
        
        // Discover
        if (preg_match('/^6(?:011|5)/', $cardNumber)) {
            return 'DISCOVER';
        }
        
        // JCB
        if (preg_match('/^(?:2131|1800|35)/', $cardNumber)) {
            return 'JCB';
        }
        
        // UnionPay
        if (preg_match('/^62/', $cardNumber)) {
            return 'UNIONPAY';
        }
        
        return 'UNKNOWN';
    }
    
    /**
     * Busca informações da BIN
     */
    public function getBinInfo($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        // Tenta encontrar a BIN exata (6 dígitos)
        $bin6 = substr($cardNumber, 0, 6);
        if (isset($this->bins[$bin6])) {
            return $this->bins[$bin6];
        }
        
        // Tenta encontrar BIN de 4 dígitos
        $bin4 = substr($cardNumber, 0, 4);
        if (isset($this->bins[$bin4])) {
            return $this->bins[$bin4];
        }
        
        return null;
    }
    
    /**
     * Verifica se o cartão está na lista
     */
    public function isCardInList($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        return in_array($cardNumber, $this->cards);
    }
    
    /**
     * Análise completa do cartão
     */
    public function analyzeCard($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        $result = [
            'card_number' => $cardNumber,
            'formatted_number' => $this->formatCardNumber($cardNumber),
            'is_valid' => $this->isValidCard($cardNumber),
            'brand' => $this->identifyBrand($cardNumber),
            'in_list' => $this->isCardInList($cardNumber),
            'bin_info' => $this->getBinInfo($cardNumber)
        ];
        
        return $result;
    }
    
    /**
     * Formata o número do cartão para exibição
     */
    private function formatCardNumber($cardNumber) {
        return chunk_split($cardNumber, 4, ' ');
    }
    
    /**
     * Retorna todas as BINs carregadas
     */
    public function getAllBins() {
        return $this->bins;
    }
    
    /**
     * Retorna todos os cartões carregados
     */
    public function getAllCards() {
        return $this->cards;
    }
    
    /**
     * Adiciona um novo cartão à lista
     */
    public function addCard($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        if (!in_array($cardNumber, $this->cards)) {
            $this->cards[] = $cardNumber;
            return true;
        }
        return false;
    }
    
    /**
     * Remove um cartão da lista
     */
    public function removeCard($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        $key = array_search($cardNumber, $this->cards);
        if ($key !== false) {
            unset($this->cards[$key]);
            $this->cards = array_values($this->cards); // Reindexar array
            return true;
        }
        return false;
    }
    
    /**
     * Salva a lista de cartões no arquivo
     */
    public function saveCards($filename = 'cards.txt') {
        return file_put_contents($filename, implode("\n", $this->cards));
    }
}

?>
