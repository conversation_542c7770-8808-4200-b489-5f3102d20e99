<?php

class CardChecker {
    private $bins = [];
    private $cards = [];
    private $db = null;

    public function __construct($binsFile = 'bins.csv', $cardsFile = 'cards.txt', $dbFile = 'cards.db') {
        $this->loadBins($binsFile);
        $this->loadCards($cardsFile);
        $this->connectDatabase($dbFile);
    }

    /**
     * Conecta ao banco de dados SQLite
     */
    private function connectDatabase($dbFile) {
        try {
            if (file_exists($dbFile)) {
                $this->db = new PDO("sqlite:$dbFile");
                $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            }
        } catch (PDOException $e) {
            throw new Exception("Erro ao conectar ao banco de dados: " . $e->getMessage());
        }
    }
    
    /**
     * Carrega as BINs do arquivo CSV
     */
    private function loadBins($filename) {
        if (!file_exists($filename)) {
            throw new Exception("Arquivo de BINs não encontrado: $filename");
        }

        $handle = fopen($filename, 'r');
        if ($handle !== false) {
            $isFirstLine = true;

            while (($line = fgets($handle)) !== false) {
                // Pular cabeçalho
                if ($isFirstLine) {
                    $isFirstLine = false;
                    continue;
                }

                // Parse CSV com vírgulas, considerando aspas
                $data = str_getcsv(trim($line));

                if (count($data) >= 10) {
                    // Novo formato: BIN,Brand,Type,Category,Issuer,IssuerPhone,IssuerUrl,isoCode2,isoCode3,CountryName
                    $this->bins[$data[0]] = [
                        'bin' => $data[0],
                        'brand' => trim($data[1], '"'), // Remove aspas se existirem
                        'type' => trim($data[2], '"'),
                        'category' => trim($data[3], '"'),
                        'issuer' => trim($data[4], '"'),
                        'phone' => $data[5],
                        'website' => $data[6],
                        'country_code' => $data[7],
                        'iso_code3' => $data[8],
                        'country' => trim($data[9], '"'),
                        // Manter compatibilidade com formato antigo
                        'level' => trim($data[3], '"') // Category como level
                    ];
                }
            }
            fclose($handle);
        }
    }
    
    /**
     * Carrega a lista de cartões do arquivo
     */
    private function loadCards($filename) {
        if (!file_exists($filename)) {
            throw new Exception("Arquivo de cartões não encontrado: $filename");
        }
        
        $this->cards = array_filter(array_map('trim', file($filename)));
    }
    
    /**
     * Verifica se um cartão é válido usando o algoritmo de Luhn
     */
    public function isValidCard($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        if (strlen($cardNumber) < 13 || strlen($cardNumber) > 19) {
            return false;
        }
        
        $sum = 0;
        $alternate = false;
        
        for ($i = strlen($cardNumber) - 1; $i >= 0; $i--) {
            $digit = intval($cardNumber[$i]);
            
            if ($alternate) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit = ($digit % 10) + 1;
                }
            }
            
            $sum += $digit;
            $alternate = !$alternate;
        }
        
        return ($sum % 10) === 0;
    }
    
    /**
     * Identifica a bandeira do cartão baseado no número
     */
    public function identifyBrand($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        // Visa
        if (preg_match('/^4/', $cardNumber)) {
            return 'VISA';
        }
        
        // Mastercard
        if (preg_match('/^5[1-5]/', $cardNumber) || preg_match('/^2[2-7]/', $cardNumber)) {
            return 'MASTERCARD';
        }
        
        // American Express
        if (preg_match('/^3[47]/', $cardNumber)) {
            return 'AMERICAN EXPRESS';
        }
        
        // Discover
        if (preg_match('/^6(?:011|5)/', $cardNumber)) {
            return 'DISCOVER';
        }
        
        // JCB
        if (preg_match('/^(?:2131|1800|35)/', $cardNumber)) {
            return 'JCB';
        }
        
        // UnionPay
        if (preg_match('/^62/', $cardNumber)) {
            return 'UNIONPAY';
        }
        
        return 'UNKNOWN';
    }
    
    /**
     * Busca informações da BIN
     */
    public function getBinInfo($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        // Tenta encontrar a BIN exata (6 dígitos)
        $bin6 = substr($cardNumber, 0, 6);
        if (isset($this->bins[$bin6])) {
            return $this->bins[$bin6];
        }
        
        // Tenta encontrar BIN de 4 dígitos
        $bin4 = substr($cardNumber, 0, 4);
        if (isset($this->bins[$bin4])) {
            return $this->bins[$bin4];
        }
        
        return null;
    }
    
    /**
     * Verifica se o cartão está na lista
     */
    public function isCardInList($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        return in_array($cardNumber, $this->cards);
    }
    
    /**
     * Análise completa do cartão
     */
    public function analyzeCard($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        $result = [
            'card_number' => $cardNumber,
            'formatted_number' => $this->formatCardNumber($cardNumber),
            'is_valid' => $this->isValidCard($cardNumber),
            'brand' => $this->identifyBrand($cardNumber),
            'in_list' => $this->isCardInList($cardNumber),
            'bin_info' => $this->getBinInfo($cardNumber)
        ];
        
        return $result;
    }
    
    /**
     * Formata o número do cartão para exibição
     */
    private function formatCardNumber($cardNumber) {
        return chunk_split($cardNumber, 4, ' ');
    }
    
    /**
     * Retorna todas as BINs carregadas
     */
    public function getAllBins() {
        return $this->bins;
    }
    
    /**
     * Retorna todos os cartões carregados
     */
    public function getAllCards() {
        return $this->cards;
    }
    
    /**
     * Adiciona um novo cartão à lista
     */
    public function addCard($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        if (!in_array($cardNumber, $this->cards)) {
            $this->cards[] = $cardNumber;
            return true;
        }
        return false;
    }
    
    /**
     * Remove um cartão da lista
     */
    public function removeCard($cardNumber) {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        $key = array_search($cardNumber, $this->cards);
        if ($key !== false) {
            unset($this->cards[$key]);
            $this->cards = array_values($this->cards); // Reindexar array
            return true;
        }
        return false;
    }
    
    /**
     * Salva a lista de cartões no arquivo
     */
    public function saveCards($filename = 'cards.txt') {
        return file_put_contents($filename, implode("\n", $this->cards));
    }

    /**
     * Obtém todas as BINs únicas dos cartões no banco de dados organizadas por país
     */
    public function getUniqueBinsFromDatabase() {
        if (!$this->db) {
            return [];
        }

        try {
            $stmt = $this->db->query("
                SELECT DISTINCT
                    SUBSTR(numero, 1, 6) as bin,
                    COUNT(*) as total_cards
                FROM cards
                WHERE LENGTH(numero) >= 6
                GROUP BY SUBSTR(numero, 1, 6)
                ORDER BY bin
            ");

            $binsByCountry = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $binInfo = $this->getBinInfoByNumber($row['bin']);

                $country = 'Desconhecido';
                if ($binInfo && !empty($binInfo['country'])) {
                    $country = $this->translateCountry($binInfo['country']);
                }

                if (!isset($binsByCountry[$country])) {
                    $binsByCountry[$country] = [];
                }

                $binsByCountry[$country][] = [
                    'bin' => $row['bin'],
                    'total_cards' => $row['total_cards'],
                    'info' => $binInfo
                ];
            }

            // Ordenar países alfabeticamente, mas manter Brasil e Estados Unidos no topo
            uksort($binsByCountry, function($a, $b) {
                if ($a === 'Brasil') return -1;
                if ($b === 'Brasil') return 1;
                if ($a === 'Estados Unidos') return -1;
                if ($b === 'Estados Unidos') return 1;
                return strcmp($a, $b);
            });

            return $binsByCountry;
        } catch (PDOException $e) {
            throw new Exception("Erro ao buscar BINs: " . $e->getMessage());
        }
    }

    /**
     * Traduz nomes de países para português
     */
    private function translateCountry($country) {
        $translations = [
            'BRAZIL' => 'Brasil',
            'UNITED STATES' => 'Estados Unidos',
            'SPAIN' => 'Espanha',
            'JAPAN' => 'Japão',
            'CHINA' => 'China',
            'CANADA' => 'Canadá',
            'FRANCE' => 'França',
            'GERMANY' => 'Alemanha',
            'ITALY' => 'Itália',
            'UNITED KINGDOM' => 'Reino Unido',
            'ARGENTINA' => 'Argentina',
            'CHILE' => 'Chile',
            'COLOMBIA' => 'Colômbia',
            'MEXICO' => 'México',
            'PERU' => 'Peru',
            'URUGUAY' => 'Uruguai',
            'VENEZUELA' => 'Venezuela',
            'PORTUGAL' => 'Portugal',
            'NETHERLANDS' => 'Holanda',
            'SWITZERLAND' => 'Suíça',
            'AUSTRALIA' => 'Austrália',
            'SOUTH KOREA' => 'Coreia do Sul',
            'INDIA' => 'Índia',
            'RUSSIA' => 'Rússia',
            'PHILIPPINES' => 'Filipinas',
            'THAILAND' => 'Tailândia',
            'SINGAPORE' => 'Singapura',
            'MALAYSIA' => 'Malásia',
            'INDONESIA' => 'Indonésia',
            'VIETNAM' => 'Vietnã',
            'SOUTH AFRICA' => 'África do Sul',
            'EGYPT' => 'Egito',
            'TURKEY' => 'Turquia',
            'ISRAEL' => 'Israel',
            'SAUDI ARABIA' => 'Arábia Saudita',
            'UAE' => 'Emirados Árabes Unidos',
            'UNITED ARAB EMIRATES' => 'Emirados Árabes Unidos',
            'POLAND' => 'Polônia',
            'CZECH REPUBLIC' => 'República Tcheca',
            'HUNGARY' => 'Hungria',
            'ROMANIA' => 'Romênia',
            'BULGARIA' => 'Bulgária',
            'CROATIA' => 'Croácia',
            'SERBIA' => 'Sérvia',
            'UKRAINE' => 'Ucrânia',
            'BELARUS' => 'Belarus',
            'LITHUANIA' => 'Lituânia',
            'LATVIA' => 'Letônia',
            'ESTONIA' => 'Estônia',
            'FINLAND' => 'Finlândia',
            'SWEDEN' => 'Suécia',
            'NORWAY' => 'Noruega',
            'DENMARK' => 'Dinamarca',
            'ICELAND' => 'Islândia',
            'IRELAND' => 'Irlanda',
            'BELGIUM' => 'Bélgica',
            'LUXEMBOURG' => 'Luxemburgo',
            'AUSTRIA' => 'Áustria',
            'GREECE' => 'Grécia',
            'CYPRUS' => 'Chipre',
            'MALTA' => 'Malta',
            'SLOVENIA' => 'Eslovênia',
            'SLOVAKIA' => 'Eslováquia',
            'BOSNIA AND HERZEGOVINA' => 'Bósnia e Herzegovina',
            'MONTENEGRO' => 'Montenegro',
            'NORTH MACEDONIA' => 'Macedônia do Norte',
            'ALBANIA' => 'Albânia',
            'MOLDOVA' => 'Moldávia',
            'GEORGIA' => 'Geórgia',
            'ARMENIA' => 'Armênia',
            'AZERBAIJAN' => 'Azerbaijão',
            'KAZAKHSTAN' => 'Cazaquistão',
            'UZBEKISTAN' => 'Uzbequistão',
            'KYRGYZSTAN' => 'Quirguistão',
            'TAJIKISTAN' => 'Tadjiquistão',
            'TURKMENISTAN' => 'Turcomenistão',
            'MONGOLIA' => 'Mongólia',
            'NORTH KOREA' => 'Coreia do Norte',
            'TAIWAN' => 'Taiwan',
            'HONG KONG' => 'Hong Kong',
            'MACAU' => 'Macau',
            'MYANMAR' => 'Mianmar',
            'CAMBODIA' => 'Camboja',
            'LAOS' => 'Laos',
            'BRUNEI' => 'Brunei',
            'PAPUA NEW GUINEA' => 'Papua-Nova Guiné',
            'NEW ZEALAND' => 'Nova Zelândia',
            'FIJI' => 'Fiji'
        ];

        $upperCountry = strtoupper($country);
        return $translations[$upperCountry] ?? ucwords(strtolower($country));
    }

    /**
     * Filtra BINs por busca de bandeira/emissor
     */
    public function filterBinsBySearch($searchTerm, $binsByCountry) {
        if (empty($searchTerm)) {
            return $binsByCountry;
        }

        $filteredBins = [];
        $searchTerm = strtolower($searchTerm);

        foreach ($binsByCountry as $country => $bins) {
            $countryBins = [];

            foreach ($bins as $binData) {
                $match = false;

                // Buscar na BIN
                if (strpos(strtolower($binData['bin']), $searchTerm) !== false) {
                    $match = true;
                }

                // Buscar nas informações da BIN
                if ($binData['info']) {
                    $brand = strtolower($binData['info']['brand']);
                    $issuer = strtolower($binData['info']['issuer']);
                    $type = strtolower($binData['info']['type']);
                    $category = strtolower($binData['info']['category'] ?? '');
                    $country = strtolower($binData['info']['country']);

                    if (strpos($brand, $searchTerm) !== false ||
                        strpos($issuer, $searchTerm) !== false ||
                        strpos($type, $searchTerm) !== false ||
                        strpos($category, $searchTerm) !== false ||
                        strpos($country, $searchTerm) !== false) {
                        $match = true;
                    }
                }

                if ($match) {
                    $countryBins[] = $binData;
                }
            }

            if (!empty($countryBins)) {
                $filteredBins[$country] = $countryBins;
            }
        }

        return $filteredBins;
    }

    /**
     * Busca informações da BIN pelo número
     */
    private function getBinInfoByNumber($binNumber) {
        // Tenta encontrar a BIN exata (6 dígitos)
        if (isset($this->bins[$binNumber])) {
            return $this->bins[$binNumber];
        }

        // Tenta encontrar BIN de 4 dígitos
        $bin4 = substr($binNumber, 0, 4);
        if (isset($this->bins[$bin4])) {
            return $this->bins[$bin4];
        }

        return null;
    }

    /**
     * Obtém cartões por BIN do banco de dados
     */
    public function getCardsByBin($bin) {
        if (!$this->db) {
            return [];
        }

        try {
            $stmt = $this->db->prepare("
                SELECT numero, validade, cvv, nome, cpf, origem_arquivo
                FROM cards
                WHERE SUBSTR(numero, 1, 6) = ?
                ORDER BY numero
            ");

            $stmt->execute([$bin]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("Erro ao buscar cartões: " . $e->getMessage());
        }
    }

    /**
     * Obtém estatísticas do banco de dados
     */
    public function getDatabaseStats() {
        if (!$this->db) {
            return [
                'total_cards' => 0,
                'unique_bins' => 0,
                'origins' => []
            ];
        }

        try {
            // Total de cartões
            $stmt = $this->db->query("SELECT COUNT(*) as total FROM cards");
            $totalCards = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

            // BINs únicas
            $stmt = $this->db->query("SELECT COUNT(DISTINCT SUBSTR(numero, 1, 6)) as total FROM cards");
            $uniqueBins = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

            // Origens dos arquivos
            $stmt = $this->db->query("
                SELECT origem_arquivo, COUNT(*) as total
                FROM cards
                GROUP BY origem_arquivo
                ORDER BY total DESC
            ");
            $origins = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'total_cards' => $totalCards,
                'unique_bins' => $uniqueBins,
                'origins' => $origins
            ];
        } catch (PDOException $e) {
            throw new Exception("Erro ao obter estatísticas: " . $e->getMessage());
        }
    }

    /**
     * Busca cartões por termo (número, nome, etc.)
     */
    public function searchCards($term) {
        if (!$this->db) {
            return [];
        }

        try {
            $stmt = $this->db->prepare("
                SELECT numero, validade, cvv, nome, cpf, origem_arquivo
                FROM cards
                WHERE numero LIKE ?
                   OR nome LIKE ?
                   OR cpf LIKE ?
                ORDER BY numero
                LIMIT 100
            ");

            $searchTerm = "%$term%";
            $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            throw new Exception("Erro na busca: " . $e->getMessage());
        }
    }
}

?>
