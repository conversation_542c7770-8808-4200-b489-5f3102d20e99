<?php
require_once 'card_checker.php';

echo "=== TESTE DO BANCO DE DADOS SQLITE ===\n\n";

try {
    $checker = new CardChecker();
    
    // Verificar se o banco existe
    if (!file_exists('cards.db')) {
        echo "❌ Arquivo cards.db não encontrado!\n";
        echo "Certifique-se de que o arquivo cards.db está no mesmo diretório.\n\n";
        
        // Criar um banco de exemplo para teste
        echo "Criando banco de exemplo para demonstração...\n";
        $db = new PDO('sqlite:cards_example.db');
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Criar tabela
        $db->exec("
            CREATE TABLE IF NOT EXISTS cards (
                numero TEXT PRIMARY KEY,
                validade TEXT,
                cvv TEXT,
                nome TEXT,
                cpf TEXT,
                origem_arquivo TEXT
            )
        ");
        
        // Inserir dados de exemplo
        $stmt = $db->prepare("
            INSERT OR REPLACE INTO cards (numero, validade, cvv, nome, cpf, origem_arquivo) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $exampleCards = [
            ['****************', '12/25', '123', 'JOAO SILVA', '12345678901', 'exemplo1.txt'],
            ['****************', '01/26', '456', 'MARIA SANTOS', '98765432109', 'exemplo1.txt'],
            ['****************', '03/27', '789', 'PEDRO OLIVEIRA', '11122233344', 'exemplo2.txt'],
            ['****************', '06/25', '321', 'ANA COSTA', '55566677788', 'exemplo2.txt'],
            ['****************', '09/26', '654', 'CARLOS LIMA', '99988877766', 'exemplo3.txt'],
            ['****************', '12/27', '987', 'LUCIA FERREIRA', '44433322211', 'exemplo3.txt'],
            ['3400000000000000', '04/25', '111', 'ROBERTO ALVES', '12312312312', 'exemplo4.txt'],
            ['1800030000000000', '08/26', '222', 'FERNANDA ROCHA', '32132132132', 'exemplo5.txt'],
        ];
        
        foreach ($exampleCards as $card) {
            $stmt->execute($card);
        }
        
        echo "✅ Banco de exemplo criado: cards_example.db\n";
        echo "Para testar, renomeie para cards.db ou modifique o código.\n\n";
        
        // Testar com o banco de exemplo
        $checker = new CardChecker('bins.csv', 'cards.txt', 'cards_example.db');
    }
    
    // Obter estatísticas
    echo "=== ESTATÍSTICAS DO BANCO ===\n";
    $stats = $checker->getDatabaseStats();
    echo "Total de cartões: " . number_format($stats['total_cards']) . "\n";
    echo "BINs únicas: " . number_format($stats['unique_bins']) . "\n";
    echo "Arquivos de origem: " . count($stats['origins']) . "\n\n";
    
    if (!empty($stats['origins'])) {
        echo "Distribuição por arquivo:\n";
        foreach ($stats['origins'] as $origin) {
            echo "  - " . $origin['origem_arquivo'] . ": " . number_format($origin['total']) . " cartões\n";
        }
        echo "\n";
    }
    
    // Listar BINs por país
    echo "=== BINS POR PAÍS ===\n";
    $binsByCountry = $checker->getUniqueBinsFromDatabase();

    if (empty($binsByCountry)) {
        echo "Nenhuma BIN encontrada no banco de dados.\n\n";
    } else {
        foreach ($binsByCountry as $country => $bins) {
            echo "País: $country (" . count($bins) . " BINs)\n";
            echo str_repeat("-", 40) . "\n";

            foreach ($bins as $binData) {
                echo "  BIN: " . $binData['bin'] . " (" . $binData['total_cards'] . " cartões)\n";

                if ($binData['info']) {
                    echo "    - Bandeira: " . $binData['info']['brand'] . "\n";
                    echo "    - Emissor: " . $binData['info']['issuer'] . "\n";
                } else {
                    echo "    - Informações da BIN não encontradas\n";
                }
            }
            echo "\n";
        }
    }
    
    // Testar busca por BIN específica
    if (!empty($binsByCountry)) {
        $firstCountry = array_keys($binsByCountry)[0];
        $firstBin = $binsByCountry[$firstCountry][0]['bin'];
        echo "=== TESTE: CARTÕES DA BIN $firstBin ($firstCountry) ===\n";

        $cards = $checker->getCardsByBin($firstBin);

        if (empty($cards)) {
            echo "Nenhum cartão encontrado para esta BIN.\n";
        } else {
            echo "Encontrados " . count($cards) . " cartões:\n";
            foreach ($cards as $card) {
                echo "  - " . chunk_split($card['numero'], 4, ' ') . " | " .
                     $card['validade'] . " | " .
                     $card['nome'] . " | " .
                     $card['origem_arquivo'] . "\n";
            }
        }
        echo "\n";
    }
    
    // Testar busca
    echo "=== TESTE: BUSCA DE CARTÕES ===\n";
    $searchResults = $checker->searchCards('JOAO');
    echo "Busca por 'JOAO': " . count($searchResults) . " resultados\n";

    foreach ($searchResults as $card) {
        echo "  - " . chunk_split($card['numero'], 4, ' ') . " | " . $card['nome'] . "\n";
    }
    echo "\n";

    // Testar filtro de BINs
    echo "=== TESTE: FILTRO DE BINS ===\n";
    $filteredBins = $checker->filterBinsBySearch('visa', $binsByCountry);
    echo "Filtro por 'visa': " . array_sum(array_map('count', $filteredBins)) . " BINs encontradas\n";

    foreach ($filteredBins as $country => $bins) {
        echo "  $country: " . count($bins) . " BINs\n";
        foreach ($bins as $binData) {
            echo "    - " . $binData['bin'] . " (" . $binData['info']['brand'] . ")\n";
        }
    }
    
    echo "\n=== TESTE CONCLUÍDO COM SUCESSO! ===\n";
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
    echo "\nVerifique se:\n";
    echo "1. O arquivo cards.db existe no diretório\n";
    echo "2. O PHP tem permissões para ler o arquivo\n";
    echo "3. O SQLite está habilitado no PHP\n";
    echo "4. A estrutura da tabela está correta\n";
}

echo "\n=== INSTRUÇÕES ===\n";
echo "1. Certifique-se de que o arquivo cards.db está presente\n";
echo "2. Execute: php -S localhost:8000\n";
echo "3. Acesse: http://localhost:8000\n";
echo "4. Selecione uma BIN para ver os cartões\n";
?>
