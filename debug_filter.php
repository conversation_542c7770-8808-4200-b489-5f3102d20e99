<?php
require_once 'card_checker.php';

echo "=== DEBUG DO FILTRO ELO ===\n\n";

try {
    $checker = new CardChecker();
    
    // Obter todas as BINs
    $allBins = $checker->getUniqueBinsFromDatabase();
    
    echo "📊 TODAS AS BINS ENCONTRADAS:\n";
    foreach ($allBins as $country => $bins) {
        echo "\n🌍 $country:\n";
        foreach ($bins as $binData) {
            if ($binData['info']) {
                $brand = $binData['info']['brand'];
                $issuer = $binData['info']['issuer'];
                
                echo "  - BIN: " . $binData['bin'] . "\n";
                echo "    Bandeira: '$brand'\n";
                echo "    Emissor: '$issuer'\n";
                
                // Verificar se contém "elo" em algum lugar
                if (stripos($brand, 'elo') !== false || stripos($issuer, 'elo') !== false) {
                    echo "    ⚠️  CONTÉM 'ELO': ";
                    if (stripos($brand, 'elo') !== false) echo "na bandeira ";
                    if (stripos($issuer, 'elo') !== false) echo "no emissor ";
                    echo "\n";
                }
                echo "\n";
            }
        }
    }
    
    echo "\n=== TESTE DO FILTRO 'ELO' ===\n";
    $filteredBins = $checker->filterBinsBySearch('elo', $allBins);
    
    if (empty($filteredBins)) {
        echo "✅ PERFEITO! Nenhuma BIN retornada para 'elo'\n";
    } else {
        echo "❌ PROBLEMA! Ainda retornando BINs:\n";
        foreach ($filteredBins as $country => $bins) {
            echo "\n$country:\n";
            foreach ($bins as $binData) {
                if ($binData['info']) {
                    echo "  - " . $binData['bin'] . " | " . 
                         $binData['info']['brand'] . " | " . 
                         $binData['info']['issuer'] . "\n";
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
}
?>
