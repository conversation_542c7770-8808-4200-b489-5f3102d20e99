<?php
require_once 'card_checker.php';

echo "=== TESTE DO FILTRO DE BINS ===\n\n";

try {
    $checker = new CardChecker();
    
    // Obter todas as BINs
    $allBins = $checker->getUniqueBinsFromDatabase();
    
    echo "📊 Total de BINs por país:\n";
    foreach ($allBins as $country => $bins) {
        echo "- $country: " . count($bins) . " BINs\n";
    }
    echo "\n";
    
    // Testar filtros específicos
    $testFilters = ['elo', 'visa', 'mastercard', 'bradesco', 'brasil'];
    
    foreach ($testFilters as $filter) {
        echo "🔍 FILTRO: '$filter'\n";
        echo str_repeat("-", 40) . "\n";
        
        $filteredBins = $checker->filterBinsBySearch($filter, $allBins);
        
        if (empty($filteredBins)) {
            echo "Nenhuma BIN encontrada.\n\n";
            continue;
        }
        
        foreach ($filteredBins as $country => $bins) {
            echo "🌍 $country (" . count($bins) . " BINs):\n";
            
            foreach ($bins as $binData) {
                if ($binData['info']) {
                    echo "  - " . $binData['bin'] . " | " . 
                         $binData['info']['brand'] . " | " . 
                         $binData['info']['issuer'] . " | " . 
                         $binData['total_cards'] . " cartões\n";
                } else {
                    echo "  - " . $binData['bin'] . " | Info não disponível | " . 
                         $binData['total_cards'] . " cartões\n";
                }
            }
            echo "\n";
        }
    }
    
    // Teste específico para ELO
    echo "🎯 TESTE ESPECÍFICO PARA ELO:\n";
    echo str_repeat("=", 50) . "\n";
    
    $eloBins = $checker->filterBinsBySearch('elo', $allBins);
    
    foreach ($eloBins as $country => $bins) {
        echo "País: $country\n";
        foreach ($bins as $binData) {
            if ($binData['info']) {
                $brand = $binData['info']['brand'];
                $issuer = $binData['info']['issuer'];
                
                echo "  BIN: " . $binData['bin'] . "\n";
                echo "  Bandeira: '$brand'\n";
                echo "  Emissor: '$issuer'\n";
                echo "  Cartões: " . $binData['total_cards'] . "\n";
                
                // Verificar se realmente deveria aparecer
                if (strtolower($brand) === 'elo') {
                    echo "  ✅ CORRETO: Bandeira é ELO\n";
                } elseif (preg_match('/\belo\b/i', $brand)) {
                    echo "  ✅ CORRETO: Contém palavra ELO\n";
                } else {
                    echo "  ❌ ERRO: Não deveria aparecer (contém ELO em: '$issuer')\n";
                }
                echo "\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
}
?>
