<?php
require_once 'card_checker.php';

echo "=== TESTE DO SISTEMA CORRIGIDO ===\n\n";

try {
    // Testar se o sistema inicializa corretamente
    $checker = new CardChecker();
    echo "✅ Sistema inicializado com sucesso!\n\n";
    
    // Testar estatísticas
    $stats = $checker->getDatabaseStats();
    echo "📊 ESTATÍSTICAS:\n";
    echo "- Total de cartões: " . number_format($stats['total_cards']) . "\n";
    echo "- BINs únicas: " . number_format($stats['unique_bins']) . "\n";
    echo "- Arquivos de origem: " . count($stats['origins']) . "\n\n";
    
    // Testar BINs por país
    $binsByCountry = $checker->getUniqueBinsFromDatabase();
    echo "🌍 PAÍSES ENCONTRADOS:\n";
    foreach ($binsByCountry as $country => $bins) {
        echo "- $country: " . count($bins) . " BINs\n";
    }
    echo "\n";
    
    // Testar análise de cartão
    echo "🔍 TESTE DE ANÁLISE DE CARTÃO:\n";
    $testCard = '****************';
    $analysis = $checker->analyzeCard($testCard);
    
    echo "Cartão: " . $analysis['formatted_number'] . "\n";
    echo "Válido: " . ($analysis['is_valid'] ? 'Sim' : 'Não') . "\n";
    echo "Bandeira: " . $analysis['brand'] . "\n";
    echo "No banco: " . ($analysis['in_database'] ? 'Sim' : 'Não') . "\n";
    
    if ($analysis['bin_info']) {
        echo "BIN Info: " . $analysis['bin_info']['brand'] . " - " . $analysis['bin_info']['issuer'] . "\n";
    }
    
    echo "\n✅ TODOS OS TESTES PASSARAM!\n";
    echo "🚀 Sistema pronto para uso!\n";
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
    echo "Verifique se todos os arquivos estão presentes:\n";
    echo "- card_checker.php\n";
    echo "- bins.csv\n";
    echo "- cards.db\n";
}
?>
